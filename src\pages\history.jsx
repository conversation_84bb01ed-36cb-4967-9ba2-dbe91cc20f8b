import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";

export default function History() {
  const [bookings, setBookings] = useState([]);
  const [user, setUser] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    // Check if user is logged in
    const userData = localStorage.getItem('user');
    if (!userData) {
      navigate('/login');
      return;
    }

    setUser(JSON.parse(userData));

    // Get bookings from localStorage
    const allBookings = JSON.parse(localStorage.getItem('bookings') || '[]');
    setBookings(allBookings);
  }, [navigate]);



  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-ubud-dark-green mb-4">Please login to view your booking history</h2>
          <button
            onClick={() => navigate('/login')}
            className="bg-ubud-dark-green text-white px-6 py-3 rounded-lg hover:bg-ubud-light-green transition-colors"
          >
            Go to Login
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-6 py-8">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-3xl font-bold text-ubud-dark-green mb-8 text-center">Order History</h1>

          {bookings.length === 0 ? (
            <div className="bg-white rounded-lg shadow-lg p-8 text-center">
              <div className="text-6xl mb-4">📋</div>
              <h2 className="text-2xl font-bold text-gray-600 mb-4">No Booking History</h2>
              <p className="text-gray-500 mb-6">You haven't made any bookings yet.</p>
              <button
                onClick={() => navigate('/services')}
                className="bg-ubud-dark-green text-white px-6 py-3 rounded-lg hover:bg-ubud-light-green transition-colors"
              >
                Browse Services
              </button>
            </div>
          ) : (
            <div className="space-y-6">
              {bookings.map((booking) => (
                <div key={booking.id} className="bg-white rounded-lg shadow-md border border-gray-100 overflow-hidden">
                  <div className="p-6">
                    <div className="flex justify-between items-start mb-6">
                      <div>
                        <h3 className="text-lg font-bold text-ubud-dark-green mb-1">
                          Booking #{booking.id}
                        </h3>
                        <p className="text-sm text-gray-500">
                          Booked on: {new Date(booking.bookingDate).toLocaleDateString('id-ID')}
                        </p>
                      </div>
                      <span className="px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                        Confirmed
                      </span>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                      <div>
                        <h4 className="font-semibold text-ubud-dark-green mb-3">Booking Details</h4>
                        <div className="space-y-2 text-sm text-gray-600">
                          <p><span className="font-medium text-gray-800">Name:</span> {booking.name}</p>
                          <p><span className="font-medium text-gray-800">Phone:</span> {booking.phone}</p>
                          <p><span className="font-medium text-gray-800">Date:</span> {booking.date}</p>
                        </div>
                      </div>

                      <div>
                        <h4 className="font-semibold text-ubud-dark-green mb-3">Activities</h4>
                        <div className="space-y-3">
                          {booking.selectedActivities?.map((activity, index) => (
                            <div key={index} className="flex justify-between items-start text-sm">
                              <div className="flex-1">
                                <span className="text-gray-700 font-medium">{activity.name}</span>
                                <div className="text-xs text-gray-500 mt-1">
                                  1 person × Rp. {activity.pricePerPerson?.toLocaleString('id-ID') || activity.price?.toLocaleString('id-ID')}
                                </div>
                              </div>
                              <span className="font-semibold text-ubud-dark-green ml-4">
                                Rp. {activity.price?.toLocaleString('id-ID')}
                              </span>
                            </div>
                          ))}
                          <div className="border-t border-gray-200 pt-3 mt-4">
                            <div className="flex justify-between items-center font-bold text-base">
                              <span className="text-gray-800">Total</span>
                              <span className="text-ubud-dark-green text-lg">
                                Rp. {booking.total?.toLocaleString('id-ID')}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {booking.paymentDate && (
                      <div className="mt-6 pt-4 border-t border-gray-200">
                        <p className="text-sm text-gray-600">
                          <span className="font-medium text-gray-800">Payment Date:</span> {' '}
                          {new Date(booking.paymentDate).toLocaleDateString('id-ID')}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
